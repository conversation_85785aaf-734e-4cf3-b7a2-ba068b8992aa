from google.colab import drive
drive.mount('/content/drive')

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pickle
import json
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
from pathlib import Path
warnings.filterwarnings('ignore')


device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Configuration
# Load and preprocess data using PointNet++ data path
GDRIVE_BASE = "/content/drive/MyDrive"
PROJECT_FOLDER = "pointnet_pile_detection"
project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER
data_path = project_path / "pointnet_data"

# Hyperparameters
config = {
    'batch_size': 8,
    'num_epochs': 100,
    'learning_rate': 0.0005,  # Same as PointNet++
    'num_points': 1024,
    'k': 20,
    'patience': 20,
    'weight_decay': 0.001,
    'dropout': 0.3,
    'gradient_clip': 0.5,
}

def load_pile_data(data_path):
    """Load data with all 20 features like PointNet++"""
    datasets = {}
    file_mapping = {
        'train': 'train_pointnet.pkl',
        'val': 'val_pointnet.pkl',
        'test': 'test_pointnet.pkl'
    }

    for split, filename in file_mapping.items():
        filepath = data_path / filename
        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        patches = data['points'].astype(np.float32)  # Keep all 20 features
        labels = np.array(data['labels'])

        datasets[split] = {
            'patches': patches,
            'labels': labels,
            'metadata': data.get('metadata', [])
        }

    return datasets

# Load and preprocess data using PointNet++ data path
print("Loading PointNet++ data for DGCNN ...")
datasets = load_pile_data(data_path)

if datasets is None:
    raise ValueError("Failed to load data!")

print("=== CLASS DISTRIBUTION ANALYSIS ===")

# Convert labels to NumPy arrays
label_data = {
    "train": np.array(datasets['train']['labels']),
    "val": np.array(datasets['val']['labels']),
    "test": np.array(datasets['test']['labels'])
}

# Print distribution for each split
for split, labels in label_data.items():
    counts = np.bincount(labels)
    total = len(labels)
    print(f"\n{split.capitalize()} set:")
    print(f"  Non-pile: {counts[0]} ({counts[0] / total * 100:.1f}%)")
    print(f"  Pile:     {counts[1]} ({counts[1] / total * 100:.1f}%)")

# Calculate class weights for training
train_counts = np.bincount(label_data["train"])
total_train = len(label_data["train"])
class_weights = total_train / (2 * train_counts)

print(f"\nRecommended class weights: Non-pile={class_weights[0]:.3f}, Pile={class_weights[1]:.3f}")

def analyze_geometry(patches, labels):
    def stats(label):
        idx = np.random.choice(np.where(labels == label)[0], 100, replace=False)
        extent = [np.ptp(patches[i][:, :3], axis=0) for i in idx if len(patches[i])]
        z_var = [np.std(patches[i][:, 2]) for i in idx if len(patches[i])]
        return np.mean(extent, axis=0), np.mean(z_var)

    pile_ext, pile_z = stats(1)
    non_pile_ext, non_pile_z = stats(0)

    print("\n=== GEOMETRIC CHARACTERISTICS ===")
    print(f"Pile     - Extent: {pile_ext.round(3)}, Z-Var: {pile_z:.3f}")
    print(f"Non-pile - Extent: {non_pile_ext.round(3)}, Z-Var: {non_pile_z:.3f}")

    height_diff = pile_ext[2] - non_pile_ext[2]
    z_var_diff = pile_z - non_pile_z

    print(f"\nHeight Diff: {height_diff:.3f}, Z-Var Diff: {z_var_diff:.3f}")
    if abs(height_diff) < 0.01 and abs(z_var_diff) < 0.01:
        print("WARNING: Patches are too geometrically similar. Consider preserving subtle variations.")

# Call after loading original dataset
analyze_geometry(datasets['train']['patches'], datasets['train']['labels'])

def preprocess_patches(patches, labels, num_points=1024):
    """Same preprocessing as PointNet++"""
    processed_patches = []
    processed_labels = []

    for patch, label in zip(patches, labels):
        patch = np.array(patch, dtype=np.float32)
        n_points, n_features = patch.shape

        if n_points >= num_points:
            indices = np.random.choice(n_points, num_points, replace=False)
            sampled = patch[indices]
        else:
            extra_indices = np.random.choice(n_points, num_points - n_points, replace=True)
            extra = patch[extra_indices].copy()
            if n_features >= 3:
                extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))
            sampled = np.vstack([patch, extra])

        # Normalize spatial coordinates only
        if sampled.shape[1] >= 3:
            spatial_coords = sampled[:, :3]
            max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))
            if max_dist > 0:
                sampled[:, :3] /= max_dist

        processed_patches.append(sampled)
        processed_labels.append(label)

    return np.array(processed_patches), np.array(processed_labels)

train_patches, train_labels = preprocess_patches(
    datasets['train']['patches'],
    datasets['train']['labels'],
    config['num_points']
)

val_patches, val_labels = preprocess_patches(
    datasets['val']['patches'],
    datasets['val']['labels'],
    config['num_points']
)

test_patches, test_labels = preprocess_patches(
    datasets['test']['patches'],
    datasets['test']['labels'],
    config['num_points']
)

class PileDataset(Dataset):
    def __init__(self, points, labels):
        self.points = torch.FloatTensor(points)
        self.labels = torch.LongTensor(labels)

    def __len__(self):
        return len(self.points)

    def __getitem__(self, idx):
        return self.points[idx], self.labels[idx]


# Create datasets and loaders
train_dataset = PileDataset(train_patches, train_labels)
val_dataset = PileDataset(val_patches, val_labels)
test_dataset = PileDataset(test_patches, test_labels)

train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False)

# Class distribution check
for name, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:
    print(f"{name}: {np.mean(labels)*100:.1f}% pile")

# Data quality check on a sample
print("\nData verification:")
sample = train_patches[0]
print(f"  Shape: {sample.shape}")
print(f"  Range: {sample.min():.3f} to {sample.max():.3f}")
print(f"  Mean dist from origin: {np.mean(np.linalg.norm(sample[:, :3], axis=1)):.3f}")

def knn(x, k):
    """Find k nearest neighbors"""
    inner = -2 * torch.matmul(x.transpose(2, 1), x)
    xx = torch.sum(x**2, dim=1, keepdim=True)
    pairwise_distance = -xx - inner - xx.transpose(2, 1)
    idx = pairwise_distance.topk(k=k, dim=-1)[1]
    return idx

def get_graph_feature(x, k=20, idx=None):
    """Create graph features from k-NN"""
    batch_size = x.size(0)
    num_points = x.size(2)
    x = x.view(batch_size, -1, num_points)

    if idx is None:
        idx = knn(x, k=k)

    device = x.device
    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points
    idx = idx + idx_base
    idx = idx.view(-1)

    _, num_dims, _ = x.size()
    x = x.transpose(2, 1).contiguous()
    feature = x.view(batch_size * num_points, -1)[idx, :]
    feature = feature.view(batch_size, num_points, k, num_dims)
    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)

    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()
    return feature

class EdgeConv(nn.Module):
    """EdgeConv layer"""
    def __init__(self, in_channels, out_channels, k=20):
        super(EdgeConv, self).__init__()
        self.k = k
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.LeakyReLU(negative_slope=0.2)
        )

    def forward(self, x):
        x = get_graph_feature(x, k=self.k)
        x = self.conv(x)
        x = x.max(dim=-1, keepdim=False)[0]
        return x

class DGCNN(nn.Module):
    """Fixed DGCNN with 20 feature input"""
    def __init__(self, num_classes=2, in_channels=20, k=20, dropout=0.3):
        super(DGCNN, self).__init__()
        self.k = k

        # Split input: 3D coords + features
        self.coord_conv1 = EdgeConv(3, 64, k)
        self.coord_conv2 = EdgeConv(64, 64, k)
        self.coord_conv3 = EdgeConv(64, 128, k)

        # Feature processing
        self.feature_conv = nn.Sequential(
            nn.Conv1d(in_channels - 3, 64, 1),
            nn.BatchNorm1d(64),
            nn.LeakyReLU(negative_slope=0.2)
        )

        # Combined processing
        self.conv4 = EdgeConv(128 + 64, 256, k)

        # Global feature aggregation
        self.conv5 = nn.Sequential(
            nn.Conv1d(64 + 64 + 128 + 256, 1024, kernel_size=1, bias=False),
            nn.BatchNorm1d(1024),
            nn.LeakyReLU(negative_slope=0.2)
        )

        # Classification head - simplified
        self.classifier = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.LeakyReLU(negative_slope=0.2),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.LeakyReLU(negative_slope=0.2),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )

    def forward(self, x):
        batch_size = x.size(0)

        # Split coordinates and features
        coords = x[:, :, :3].transpose(2, 1)  # (B, 3, N)
        features = x[:, :, 3:].transpose(2, 1)  # (B, 17, N)

        # Process coordinates with EdgeConv
        x1 = self.coord_conv1(coords)  # (B, 64, N)
        x2 = self.coord_conv2(x1)      # (B, 64, N)
        x3 = self.coord_conv3(x2)      # (B, 128, N)

        # Process features
        feat = self.feature_conv(features)  # (B, 64, N)

        # Combine and process
        combined = torch.cat([x3, feat], dim=1)  # (B, 192, N)
        x4 = self.conv4(combined)  # (B, 256, N)

        # Concatenate all features
        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)

        # Global feature extraction
        x = self.conv5(x)  # (B, 1024, N)
        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)

        # Classification
        x = self.classifier(x)
        return x

def train_epoch(model, loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    for data, target in loader:
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()

        output = model(data)
        loss = criterion(output, target)
        loss.backward()

        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])
        optimizer.step()

        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)

    return total_loss / len(loader), correct / total

def validate_epoch(model, loader, criterion, device):
    model.eval()
    total_loss = 0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)

    return total_loss / len(loader), correct / total


def setup_training(model, train_labels):
    """Balanced training setup like PointNet++"""
    pile_count = np.sum(train_labels)
    total_count = len(train_labels)
    pile_ratio = pile_count / total_count

    # Light class weighting like PointNet++
    if pile_ratio > 0.6:
        pos_weight = 1.2
        neg_weight = 0.8
        class_weights = torch.FloatTensor([neg_weight, pos_weight]).to(device)
        criterion = nn.CrossEntropyLoss(weight=class_weights)
    else:
        criterion = nn.CrossEntropyLoss()

    optimizer = optim.Adam(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )

    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.5)
    return criterion, optimizer, scheduler

def run_training(model,criterion,optimizer,scheduler):
    # Training loop
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    best_val_acc = 0
    patience_counter = 0

    for epoch in range(config['num_epochs']):
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
        val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)

        scheduler.step()

        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_accs.append(train_acc)
        val_accs.append(val_acc)

        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}")

        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            torch.save(model.state_dict(), 'best_dgcnn_fixed.pth')
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                print(f"Early stopping at epoch {epoch+1}")
                break

    # Test evaluation
    model.load_state_dict(torch.load('best_dgcnn_fixed.pth'))
    model.eval()
    all_preds = []
    all_targets = []

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            all_preds.extend(output.argmax(dim=1).cpu().numpy())
            all_targets.extend(target.cpu().numpy())

    # Calculate metrics
    test_accuracy = accuracy_score(all_targets, all_preds)
    test_f1 = f1_score(all_targets, all_preds)
    test_precision = precision_score(all_targets, all_preds)
    test_recall = recall_score(all_targets, all_preds)

    print(f"\nFinal Results:")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print(f"Test F1-Score: {test_f1:.4f}")
    print(f"Test Precision: {test_precision:.4f}")
    print(f"Test Recall: {test_recall:.4f}")

    return {
        'model': model,
        'accuracy': test_accuracy,
        'f1': test_f1,
        'precision': test_precision,
        'recall': test_recall,
        'history': {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_accs': train_accs,
            'val_accs': val_accs
        }
    }


# Initialize model and training
model = DGCNN(num_classes=2, in_channels=20, k=config['k'], dropout=config['dropout']).to(device)
criterion, optimizer, scheduler = setup_training(model, train_labels)
results = run_training(model, criterion, optimizer, scheduler)

model_name = "DGCNN"

# Extract training metrics
train_losses = results['train_loss']
val_losses = results['val_loss']
train_accs = results['train_acc']
val_accs = results['val_acc']
best_val_acc = max(val_accs)
epochs = range(1, len(train_losses) + 1)

# Plot training history
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))

# Loss
ax1.plot(epochs, train_losses, label='Train Loss', linewidth=2)
ax1.plot(epochs, val_losses, label='Val Loss', linewidth=2)
ax1.set(title=f'{model_name} Loss over Epochs', xlabel='Epoch', ylabel='Loss')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Accuracy
ax2.plot(epochs, train_accs, label='Train Acc', linewidth=2)
ax2.plot(epochs, val_accs, label='Val Acc', linewidth=2)
ax2.set(title=f'{model_name} Accuracy over Epochs', xlabel='Epoch', ylabel='Accuracy')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print summary
print(f"\n=== {model_name} TRAINING SUMMARY ===")
print(f"Epochs: {len(train_losses)}")
print(f"Best Val Acc: {best_val_acc:.4f}")
print(f"Final Train Loss: {train_losses[-1]:.4f}")
print(f"Final Val Loss: {val_losses[-1]:.4f}")
print(f"Final Train Acc: {train_accs[-1]:.4f}")
print(f"Final Val Acc: {val_accs[-1]:.4f}")


# Load best model if saved
load_best_model = True  # if you want to evaluate best checkpoint

if load_best_model:
    model.load_state_dict(torch.load('best_dgcnn_fixed.pth'))
    print("Best model loaded")

# Evaluate on test set
model.eval()
all_preds, all_targets = [], []

with torch.no_grad():
    for x, y in test_loader:
        x, y = x.to(device), y.to(device)
        preds = model(x).argmax(dim=1)
        all_preds += preds.cpu().tolist()
        all_targets += y.cpu().tolist()

# Metrics
print("\n=== DGCNN TEST RESULTS ===")
for name, score in zip(
    ["Accuracy", "Precision", "Recall", "F1-Score"],
    [accuracy_score(all_targets, all_preds),
     precision_score(all_targets, all_preds),
     recall_score(all_targets, all_preds),
     f1_score(all_targets, all_preds)]
):
    print(f"{name}: {score:.4f}")

from collections import Counter
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
import matplotlib.pyplot as plt

# Basic prediction stats
pred_dist, target_dist = Counter(all_preds), Counter(all_targets)
pile_ratio = lambda c, total: f"{c} ({c/total*100:.1f}%)"

print("=== PREDICTION ANALYSIS ===")
print(f"Actual pile:     {pile_ratio(target_dist[1], len(all_targets))}")
print(f"Predicted pile:  {pile_ratio(pred_dist[1], len(all_preds))}")
print("Bias detected" if pred_dist[1]/len(all_preds) > 0.95 else "Class balance OK")

# Classification report
print("\n=== PER-CLASS PERFORMANCE ===")
print(classification_report(all_targets, all_preds, target_names=['Non-pile', 'Pile']))

# Confusion matrix plot
cm = confusion_matrix(all_targets, all_preds)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=['Non-pile', 'Pile'],
            yticklabels=['Non-pile', 'Pile'])
plt.title('Confusion Matrix')
plt.ylabel('True')
plt.xlabel('Predicted')
plt.tight_layout()
plt.show()

# Set up file paths for spatial analysis (same as PointNet++)
import geopandas as gpd
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

GDRIVE_BASE = "/content/drive/MyDrive"
PROJECT_FOLDER = "pointnet_pile_detection"

project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER
data_path = project_path / "pointnet_data"

ifc_path = project_path / "GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
kml_path = project_path / "pile.kml"
test_pkl_path = data_path / "test_pointnet.pkl"
harmonized_pile_dataset = project_path / "harmonized_pile_dataset_final.csv"

print(f"Using data paths:")
print(f"  IFC: {ifc_path}")
print(f"  KML: {kml_path}")
print(f"  Test data: {test_pkl_path}")
print(f"  Pile dataset: {harmonized_pile_dataset}")

def create_dgcnn_spatial_plot(all_preds, all_targets, test_pkl_path, results_path, metrics):
    """Create DGCNN spatial plot using the same method as PointNet++."""

    # Load reference data
    ifc_df = pd.read_csv(ifc_path)
    ifc_coords = ifc_df[['X', 'Y']].values

    gdf_kml = gpd.read_file(kml_path, driver='KML')
    gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)
    gdf_kml['geometry'] = gdf_kml.geometry.centroid
    kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)

    # Load the original pile dataset
    pile_df = pd.read_csv(harmonized_pile_dataset)

    # Load test metadata
    with open(test_pkl_path, 'rb') as f:
        test_data = pickle.load(f)
    test_metadata = test_data.get('metadata', [])

    # Reconstruct coordinates using pile IDs
    pred_coords = []
    valid_indices = []

    for i, meta in enumerate(test_metadata[:len(all_preds)]):
        pile_id = meta.get('pile_id') if isinstance(meta, dict) else None

        if pile_id and pile_id in pile_df['pile_id'].values:
            pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]
            pred_coords.append([pile_row['x'], pile_row['y']])
            valid_indices.append(i)

    if len(pred_coords) == 0:
        print("ERROR: No pile IDs found in metadata!")
        return

    pred_coords = np.array(pred_coords)

    # Filter predictions to match valid coordinates
    all_preds_filtered = [all_preds[i] for i in valid_indices]
    all_targets_filtered = [all_targets[i] for i in valid_indices]

    # Calculate accuracy for SPATIAL subset (for display purposes)
    correct_mask = (np.array(all_targets_filtered) == np.array(all_preds_filtered))
    error_mask = ~correct_mask
    spatial_accuracy = np.mean(correct_mask)

    # Use the TRUE accuracy from full test set for title
    true_accuracy = metrics['accuracy']

    print(f"Reconstructed {len(pred_coords)} coordinates from pile IDs")
    print(f"Spatial subset accuracy: {spatial_accuracy:.3f} ({len(pred_coords)} samples)")
    print(f"Full test set accuracy: {true_accuracy:.3f} ({len(all_preds)} samples)")
    print(f"Coordinate range: X[{pred_coords[:, 0].min():.0f}, {pred_coords[:, 0].max():.0f}], Y[{pred_coords[:, 1].min():.0f}, {pred_coords[:, 1].max():.0f}]")

    # Create plot exactly like PointNet++
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))

    # Plot reference data
    ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1],
              s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')
    ax.scatter(kml_coords[:, 0], kml_coords[:, 1],
              s=8, alpha=0.7, color='green', marker='s', label='KML Ground Truth')

    # Plot predictions
    if np.sum(correct_mask) > 0:
        ax.scatter(pred_coords[correct_mask, 0], pred_coords[correct_mask, 1],
                  c='blue', s=30, alpha=0.8, marker='o',
                  label=f'Correct ({np.sum(correct_mask)})')

    if np.sum(error_mask) > 0:
        ax.scatter(pred_coords[error_mask, 0], pred_coords[error_mask, 1],
                  c='red', s=50, alpha=0.9, marker='x', linewidth=2,
                  label=f'Errors ({np.sum(error_mask)})')

    # Use TRUE accuracy in title, but show spatial stats in legend
    ax.set_title(f'DGCNN\nAccuracy: {true_accuracy:.3f}')
    ax.set_xlabel('X (UTM)')
    ax.set_ylabel('Y (UTM)')
    ax.legend(fontsize=8)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal', adjustable='box')

    # Add text box with full stats
    stats_text = f'Full Test Set:\n{len(all_preds)} samples\n{true_accuracy:.1%} accuracy\n\nSpatial Subset:\n{len(pred_coords)} samples\n{spatial_accuracy:.1%} accuracy'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig('/content/drive/MyDrive/dgcnn_results/dgcnn_spatial_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()

    return {
        'spatial_accuracy': spatial_accuracy,
        'spatial_samples': len(pred_coords),
        'true_accuracy': true_accuracy,
        'total_samples': len(all_preds)
    }

test_accuracy = accuracy_score(all_targets, all_preds)
test_precision = precision_score(all_targets, all_preds, average='weighted')
test_recall = recall_score(all_targets, all_preds, average='weighted')
test_f1 = f1_score(all_targets, all_preds, average='weighted')

# Create spatial analysis
dgcnn_metrics = {
    'accuracy': test_accuracy,
    'precision': test_precision,
    'recall': test_recall,
    'f1_score': test_f1
}

spatial_results = create_dgcnn_spatial_plot(all_preds, all_targets, test_pkl_path,
                                           Path('/content/drive/MyDrive/dgcnn_results'),
                                           dgcnn_metrics)

print(f"\nSpatial analysis complete. Results saved to Google Drive.")