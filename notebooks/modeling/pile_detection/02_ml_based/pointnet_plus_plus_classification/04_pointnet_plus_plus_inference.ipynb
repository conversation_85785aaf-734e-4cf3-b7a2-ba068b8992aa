# Parameters cell for Papermill execution
NEW_SITE_NAME = "althea_rpcs"
POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
OUTPUT_DIR = f"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}"

MODEL_PATH = "best_pointnet_iter4.pth"  # Path to trained model

DWG_PATH = ""  # Path to DWG file (optional)
OUTPUT_DIR = "output_runs/pointnet_plus_plus_inference"  # Output directory

# Model parameters (must match training)
CONFIDENCE_THRESHOLD = 0.3  # Confidence threshold for pile detection

GRID_SPACING = 10.0 # Grid spacing for analysis points
PATCH_SIZE = 20.0  # meters radius for patch extraction
NUM_POINTS = 1024 # KEEP 1024 to match training - CRITICAL for accuracy
BATCH_SIZE = 8  # Batch size for inference

# CRS and coordinate validation parameters
SITE_CRS = "EPSG:32615"  # Will be set by papermill

# Enhanced analysis parameters
EXTENDED_ANALYSIS = True
NEGATIVE_SAMPLE_DISTANCE = 8.0  # Distance for synthetic negatives
FULL_SITE_ANALYSIS = True  # Run on all points, not subset

# MLflow configuration
EXPERIMENT_NAME = "pointnet_plus_plus_inference"
RUN_NAME = f"inference_{NEW_SITE_NAME}"

# Display configuration
print(f"New Site Analysis Configuration:")
print(f"Site Name: {NEW_SITE_NAME}")
print(f"EPSG Configuration:")
print(f"  Inference EPSG: {SITE_CRS}")
print(f"Patch Size: {PATCH_SIZE}m radius")
print(f"Points per Patch: {NUM_POINTS}")
print(f"Model Path: {MODEL_PATH}")
print(f"Point Cloud Path: {POINT_CLOUD_PATH}")
print(f"Output Directory: {OUTPUT_DIR}")
print(f"Extended Analysis: {EXTENDED_ANALYSIS}")
print(f"Full Site Analysis: {FULL_SITE_ANALYSIS}")


import os
import json
import pickle
import torch
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
import torch.nn as nn
import matplotlib.pyplot as plt
import laspy
import open3d as o3d
import mlflow
import mlflow.pytorch
from scipy.spatial import cKDTree
from scipy.spatial.distance import pdist
import seaborn as sns

warnings.filterwarnings('ignore')


def setup_environment():
    """Setup output directory and MLflow tracking"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    if mlflow.active_run() is not None:
        mlflow.end_run()
    
    mlflow.set_experiment(EXPERIMENT_NAME)
    mlflow.start_run(run_name=RUN_NAME)
    
    # Log parameters
    params = {
        "site_name": NEW_SITE_NAME,
        "patch_size": PATCH_SIZE,
        "num_points": NUM_POINTS,
        "confidence_threshold": CONFIDENCE_THRESHOLD,
        "batch_size": BATCH_SIZE,
        "grid_spacing": GRID_SPACING
    }
    for key, value in params.items():
        mlflow.log_param(key, value)

class PointNetSetAbstraction(nn.Module):
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        """Forward pass of PointNet++ Set Abstraction layer"""
        B, N, C = xyz.shape
        
        if self.group_all:
            new_xyz = torch.zeros(B, 1, C).to(xyz.device)
            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)
            
            if points is not None:
                grouped_points = points[torch.arange(B)[:, None, None], idx]
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm
        
        new_points = new_points.permute(0, 3, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = torch.relu(bn(conv(new_points)))
        
        new_points = torch.max(new_points, 2)[0]
        new_points = new_points.permute(0, 2, 1)
        
        return new_xyz, new_points

class PointNetPlusPlus(nn.Module):
    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):  # ✅ CRITICAL: in_channels=20
        super(PointNetPlusPlus, self).__init__()

        # Set Abstraction Layers (SAME AS TRAINING)
        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)
        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)
        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)

        # Classification head (SAME AS TRAINING)
        self.fc1 = nn.Linear(1024, 512)
        self.bn1 = nn.BatchNorm1d(512)
        self.drop1 = nn.Dropout(dropout)

        self.fc2 = nn.Linear(512, 256)
        self.bn2 = nn.BatchNorm1d(256)
        self.drop2 = nn.Dropout(dropout)

        self.fc3 = nn.Linear(256, 64)
        self.bn3 = nn.BatchNorm1d(64)
        self.drop3 = nn.Dropout(dropout * 0.6)

        self.fc4 = nn.Linear(64, num_classes)

    def forward(self, xyz):
        # Input shape: (B, N, C), C = in_channels (20 features)
        if len(xyz.shape) == 4:
            xyz = xyz.squeeze(1)

        B, N, C = xyz.shape

        # Split input into xyz coords and features (SAME AS TRAINING)
        coords = xyz[:, :, :3]       # (B, N, 3)
        features = xyz[:, :, 3:]     # (B, N, C-3) = (B, N, 17)

        # Pass through SA layers
        l1_xyz, l1_points = self.sa1(coords, features)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)

        global_feat = l3_points.view(B, -1)

        # Classification head
        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))
        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))
        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))
        x = self.fc4(x)

        return x

def extract_patch_features(point_cloud, center_xy, radius=20.0, num_points=1024, kdtree=None):
    """Extract patch features EXACTLY matching training preprocessing"""
    center_x, center_y = center_xy
    
    if kdtree is not None:
        indices = kdtree.query_ball_point([center_x, center_y], radius)
        if len(indices) < 50:
            return None
        patch_xyz = point_cloud[indices]
    else:
        distances_2d = np.sqrt((point_cloud[:, 0] - center_x)**2 + 
                              (point_cloud[:, 1] - center_y)**2)
        mask = distances_2d <= radius
        patch_xyz = point_cloud[mask]
        
        if len(patch_xyz) < 50:
            return None
    
    # MATCH training preprocessing exactly - use same feature engineering as training data creation
    x, y, z = patch_xyz[:, 0], patch_xyz[:, 1], patch_xyz[:, 2]
    
    # Calculate patch statistics
    patch_center = np.mean(patch_xyz, axis=0)
    z_mean, z_std = z.mean(), z.std() + 1e-6
    z_min, z_max = z.min(), z.max()
    
    # Distance calculations
    dist_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
    
    # RECREATE EXACT TRAINING FEATURES - these come from the original data creation process
    features_list = []
    for i, point in enumerate(patch_xyz):
        px, py, pz = point
        
        # Match the exact feature engineering from training data preparation
        feature_vector = [
            px,  # 0: raw X coordinate
            py - center_y,  # 1: relative Y (small values like training)
            pz,  # 2: raw Z coordinate  
            (pz - z_mean) / z_std,  # 3: height_norm
            dist_to_center[i],  # 4: distance_norm
            len(patch_xyz) / 1000.0,  # 5: density
            px - center_x,  # 6: relative_x
            py - center_y,  # 7: relative_y  
            pz - patch_center[2],  # 8: relative_z
            pz - z_min,  # 9: height_above_min
            z_max - pz,  # 10: depth_below_max
            abs(pz - z_mean),  # 11: abs_height_deviation
            np.sqrt(px**2 + py**2),  # 12: distance_from_origin
            np.arctan2(py - center_y, px - center_x),  # 13: angle
            (px - center_x) * (py - center_y),  # 14: interaction
            px - patch_center[0],  # 15: patch_relative_x
            py - patch_center[1],  # 16: patch_relative_y
            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio
            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance
            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation
        ]
        features_list.append(feature_vector)
    
    patch_features = np.array(features_list, dtype=np.float32)
    
    # EXACT SAME SAMPLING LOGIC AS TRAINING
    if len(patch_features) >= num_points:
        # Distance-weighted sampling like training
        distances = patch_features[:, 4]  # distance_norm column
        probabilities = 1 / (distances + 0.1)
        probabilities /= probabilities.sum()
        
        sampled_indices = np.random.choice(len(patch_features), num_points, 
                                         replace=False, p=probabilities)
        sampled = patch_features[sampled_indices]
    else:
        # Upsample with weighted selection like training
        upsampled = patch_features.copy()
        needed = num_points - len(patch_features)
        
        for _ in range(needed):
            distances = patch_features[:, 4]
            weights = 1 / (distances + 0.1)
            weights /= weights.sum()
            source_idx = np.random.choice(len(patch_features), p=weights)
            
            new_point = patch_features[source_idx].copy()
            new_point[:3] += np.random.normal(0, 0.02, 3)  # Add noise to spatial like training
            upsampled = np.vstack([upsampled, new_point])
        
        sampled = upsampled[:num_points]
    
    # EXACT SAME NORMALIZATION AS TRAINING
    # Training shows: sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1
    spatial_coords = sampled[:, :3]
    spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))
    if spatial_extent > 0:
        sampled[:, :3] /= spatial_extent
    
    return sampled



def visualize_results(results_df, output_dir, site_name):
    """Create visualization of pile detection results"""
    if results_df.empty:
        print("No results to visualize")
        return
    
    pile_detections = results_df[results_df['prediction'] == 'PILE']
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Probability heatmap
    sc1 = ax1.scatter(
        results_df['x'], results_df['y'],
        c=results_df['pile_probability'],
        cmap='viridis', s=25, alpha=0.8
    )
    ax1.set_title('Pile Probability Heatmap')
    ax1.set_xlabel('X Coordinate')
    ax1.set_ylabel('Y Coordinate')
    plt.colorbar(sc1, ax=ax1, label='Probability')
    
    # Plot 2: Pile classifications
    ax2.scatter(
        pile_detections['x'], pile_detections['y'],
        color='darkgreen', label='Pile Detections',
        s=30, alpha=0.8
    )
    
    non_pile = results_df[results_df['prediction'] == 'NON-PILE']
    if not non_pile.empty:
        ax2.scatter(
            non_pile['x'], non_pile['y'],
            color='gray', label='Non-Pile',
            s=15, alpha=0.4
        )
    
    ax2.set_title('Pile Classification Results')
    ax2.set_xlabel('X Coordinate')
    ax2.set_ylabel('Y Coordinate')
    ax2.legend()
    
    plt.tight_layout()
    
    plot_path = Pat    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Saved visualization to: {plot_path}")
    
    plt.show()
    return str(plot_path)

def export_results(results_df, output_dir, site_name, model_path, patch_size, confidence_threshold):
    """Export results and summary statistics"""
    if results_df.empty:
        print("No results to export")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Export main results
    output_file = Path(output_dir) / f"{site_name}_pile_detections_{timestamp}.csv"
    results_df.to_csv(output_file, index=False)
    print(f"Results exported to: {output_file}")
    
    # Create summary statistics
    pile_detections = results_df[results_df['prediction'] == 'PILE']
    
    summary = {
        'site_name': site_name,
        'analysis_timestamp': timestamp,
        'total_analysis_points': len(results_df),
        'pile_detections': len(pile_detections),
        'detection_rate': len(pile_detections) / len(results_df),
        'average_pile_probability': float(results_df['pile_probability'].mean()),
        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),
        'model_path': model_path,
        'patch_size_meters': patch_size,
        'confidence_threshold': confidence_threshold
    }
    
    summary_file = Path(output_dir) / f"{site_name}_analysis_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Summary statistics saved to: {summary_file}")
    
    # Log to MLflow
    for key, value in summary.items():
        if isinstance(value, (int, float)):
            mlflow.log_metric(key, value)
    
    mlflow.log_artifact(str(output_file))
    mlflow.log_artifact(str(summary_file))
    
    return summary



def filter_valid_grid_points(point_cloud, grid_points, patch_size, min_points):
    valid_grid = []
    for pt in grid_points:
        patch = extract_patch_around_point(point_cloud, pt, radius=patch_size, num_points=min_points)
        if patch is not None:
            valid_grid.append(pt)
    return np.array(valid_grid)

# Analyse Density of Point Cloud
total_points = len(point_cloud)
xyz_min = point_cloud[:, :3].min(axis=0)
xyz_max = point_cloud[:, :3].max(axis=0)
bounds = xyz_max - xyz_min
volume = bounds[0] * bounds[1]  # area in 2D

density_2d = total_points / volume  # points per m²
print(f"Point cloud density: {density_2d:.2f} points/m²")

# Analyse Spacing of Point Cloud
import scipy.spatial

kdtree = scipy.spatial.cKDTree(point_cloud[:, :3])
dists, _ = kdtree.query(point_cloud[:, :3], k=2)  # k=2 includes self and nearest neighbor
avg_spacing = np.mean(dists[:, 1])  # skip distance to self
print(f"Average spacing between points: {avg_spacing:.3f} m")

# # Create analysis grid
# grid_points = create_analysis_grid(point_cloud, grid_spacing=GRID_SPACING)
# print(f"Created analysis grid with {len(grid_points)} analysis points")
# print(f"Grid spacing: {GRID_SPACING}m")

# mlflow.log_metric("analysis_grid_points", len(grid_points))

# # Filter invalid ones
# valid_grid_points = filter_valid_grid_points(
#     point_cloud, grid_points, patch_size=PATCH_SIZE, min_points=NUM_POINTS
# )
# print(f"Filtered to {len(valid_grid_points)} valid analysis points")

# # Extract patches around grid points
# sample_center = valid_grid_points[len(valid_grid_points)//2]
# #sample_center = grid_points[len(grid_points)//2]  # Middle of grid
# sample_patch = extract_patch_around_point(point_cloud, sample_center, 
#                                         radius=PATCH_SIZE, num_points=NUM_POINTS)

# if sample_patch is not None:
#     print(f"Successfully extracted sample patch with shape: {sample_patch.shape}")
#     print(f"Sample patch statistics:")
#     print(f"  X range: {sample_patch[:, 0].min():.3f} to {sample_patch[:, 0].max():.3f}")
#     print(f"  Y range: {sample_patch[:, 1].min():.3f} to {sample_patch[:, 1].max():.3f}")
#     print(f"  Z range: {sample_patch[:, 2].min():.3f} to {sample_patch[:, 2].max():.3f}")
# else:
#     print("Failed to extract sample patch - insufficient points in area")

print("Creating smart analysis grid...")
grid_points = create_analysis_smart_grid(point_cloud, grid_spacing=GRID_SPACING)

# Skip filtering entirely - let batch processing handle failures
print("Skipping pre-filtering - will handle failures during inference")

# Test with one sample first
if len(grid_points) > 0:
    sample_center = grid_points[len(grid_points)//2]
    sample_patch = extract_patch_around_point(point_cloud, sample_center, 
                                            radius=PATCH_SIZE, num_points=NUM_POINTS)
    
    if sample_patch is not None:
        print(f"✓ Sample patch test successful: {sample_patch.shape}")
    else:
        print("⚠ Sample patch failed - may need to adjust parameters")

# Run inference on sample patch
if sample_patch is not None:
    print(f"Sample patch shape before inference: {sample_patch.shape}")
    
    # Verify we have exactly 20 features
    if sample_patch.shape[1] != 20:
        print(f"WARNING: Expected 20 features, got {sample_patch.shape[1]}")

    with torch.no_grad():
        # Convert to tensor and add batch dimension
        patch_tensor = torch.FloatTensor(sample_patch).unsqueeze(0).to(device)
        
        # Run inference
        output = model(patch_tensor)
        probabilities = torch.softmax(output, dim=1)
        
        pile_probability = probabilities[0, 1].cpu().item()
        prediction = "PILE" if pile_probability > CONFIDENCE_THRESHOLD else "NON-PILE"
        
        print(f"Sample patch inference:")
        print(f"  Pile probability: {pile_probability:.4f}")
        print(f"  Prediction: {prediction}")
        print(f"  Confidence threshold: {CONFIDENCE_THRESHOLD}")

        # Diagnostic info
        print(f"  Raw model output: {output[0].cpu().numpy()}")
        print(f"  Probabilities: {probabilities[0].cpu().numpy()}")

def process_site_batch(point_cloud, grid_points, model, device, 
                               batch_size=8, radius=3.0, num_points=1024):  
    """
    Process multiple grid points with 20-feature patches
    """
    results = []
    
    for i in range(0, len(grid_points), batch_size):
        batch_centers = grid_points[i:i+batch_size]
        batch_patches = []
        valid_indices = []
        
        # Extract patches for batch
        for j, center in enumerate(batch_centers):
            patch = extract_patch_around_point(
                point_cloud, center, radius, num_points
            )
            if patch is not None:
                batch_patches.append(patch)
                valid_indices.append(i + j)
        
        if len(batch_patches) == 0:
            continue
        
        # Convert to tensor
        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)
        
        # Run inference
        with torch.no_grad():
            outputs = model(batch_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            pile_probs = probabilities[:, 1].cpu().numpy()
        
        # Store results
        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):
            center = grid_points[idx]
            results.append({
                'grid_index': idx,
                'x': center[0],
                'y': center[1],
                'pile_probability': prob,
                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'
            })
    
    return results

def process_site_batch_smart(point_cloud, grid_points, model, device,
                             batch_size=8, radius=PATCH_SIZE, num_points=NUM_POINTS):
    """
    Batch inference across site using smart grid
    Returns a DataFrame of results
    """
    results = []

    for i in range(0, len(grid_points), batch_size):
        batch_centers = grid_points[i:i+batch_size]
        batch_patches = []
        valid_indices = []

        # Extract patches
        for j, center in enumerate(batch_centers):
            patch = extract_patch_around_point(point_cloud, center, radius, num_points)
            if patch is not None:
                batch_patches.append(patch)
                valid_indices.append(i + j)

        if len(batch_patches) == 0:
            continue

        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)

        with torch.no_grad():
            outputs = model(batch_tensor)
            probs = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()

        for j, prob in enumerate(probs):
            idx = valid_indices[j]
            x, y = grid_points[idx]
            results.append({
                "x": x,
                "y": y,
                "pile_probability": prob,
                "prediction": "PILE" if prob > CONFIDENCE_THRESHOLD else "NON-PILE"
            })

    return pd.DataFrame(results)


# Run site-wide analysis on subset for testing
print("Running site-wide analysis on sample grid...")
SAMPLE_GRID_SIZE = 20  # bring it down if it's too slow

print(f"Grid points: {len(grid_points)}")
if len(grid_points) > SAMPLE_GRID_SIZE:
    print(f"Using first {SAMPLE_GRID_SIZE} points for testing")
    test_grid = grid_points[:SAMPLE_GRID_SIZE]
else:
    test_grid = grid_points

# Updated call: now returns a DataFrame directly
results_df = process_site_batch_smart(
    point_cloud,   # or point_cloud if already filtered
    grid_points=test_grid,
    model=model,
    device=device,
    batch_size=8,
    radius=PATCH_SIZE,
    num_points=NUM_POINTS
)

print(f"Processed {len(results_df)} analysis points")

# %%
# Analyze results
if not results_df.empty:
    pile_detections = results_df[results_df['prediction'] == 'PILE']
    non_pile_detections = results_df[results_df['prediction'] == 'NON-PILE']
    
    print(f"\nAnalysis Results Summary:")
    print(f"  Total analysis points: {len(results_df)}")
    print(f"  Pile detections: {len(pile_detections)} ({len(pile_detections)/len(results_df)*100:.1f}%)")
    print(f"  Non-pile areas: {len(non_pile_detections)} ({len(non_pile_detections)/len(results_df)*100:.1f}%)")
    print(f"  Average pile probability: {results_df['pile_probability'].mean():.4f}")
    print(f"  High confidence piles (>0.9): {sum(results_df['pile_probability'] > 0.9)}")
else:
    print("No valid predictions returned.")


print(results_df.head())

# Point cloud bounds
pcd_xyz =point_cloud[:, :3]
print("Point Cloud Bounds (X, Y):", pcd_xyz[:, :2].min(axis=0), pcd_xyz[:, :2].max(axis=0))

from scipy.spatial import cKDTree
import open3d as o3d
import numpy as np

# Build KD-tree on XY of original point cloud
pcd_xyz = point_cloud[:, :3]
pcd_tree = cKDTree(pcd_xyz[:, :2])  # only XY for matching

# Pile X,Y from detection DataFrame
pile_xy = results_df[['x', 'y']].values

# Find nearest neighbor indices in point cloud
_, idx = pcd_tree.query(pile_xy, k=1)

# Get correct Z-values from original point cloud
z_vals = pcd_xyz[idx, 2]  # use matched index to extract Z

# Stack to get full XYZ for pile detections
pile_with_z = np.hstack((pile_xy, z_vals.reshape(-1, 1)))

# Create red pile point cloud
pile_colors = np.zeros((pile_with_z.shape[0], 3))
pile_colors[:, 0] = 1.0  # Red

pile_pcd = o3d.geometry.PointCloud()
pile_pcd.points = o3d.utility.Vector3dVector(pile_with_z)
pile_pcd.colors = o3d.utility.Vector3dVector(pile_colors)

pile_pcd.paint_uniform_color([1, 0, 0])  # Red
pile_pcd.estimate_normals()
pile_pcd.normalize_normals()

# Convert original point cloud (NumPy) to Open3D
orig_pcd = o3d.geometry.PointCloud()
orig_pcd.points = o3d.utility.Vector3dVector(pcd_xyz)
orig_pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Grey

# Overlay and show
o3d.visualization.draw_geometries([orig_pcd, pile_pcd])


print("Pile X Range:", pile_xy[:, 0].min(), pile_xy[:, 0].max())
print("Pile Y Range:", pile_xy[:, 1].min(), pile_xy[:, 1].max())
print("PointCloud X Range:", pcd_xyz[:, 0].min(), pcd_xyz[:, 0].max())
print("PointCloud Y Range:", pcd_xyz[:, 1].min(), pcd_xyz[:, 1].max())
